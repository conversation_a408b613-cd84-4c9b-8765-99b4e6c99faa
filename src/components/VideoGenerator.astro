---
import type { GeneratedVideo, VideoGenerationProgress } from '../types';
---

<div class="video-generator">
  <div class="generation-status">
    <h3 style="margin-bottom: 20px; color: #333;">Generating Videos</h3>
    
    <div class="progress-bar">
      <div id="progress-fill" class="progress-fill" style="width: 0%;"></div>
    </div>
    
    <div class="progress-text">
      <div id="current-locale">Preparing...</div>
      <div id="current-step" style="font-size: 14px; color: #666; margin-top: 5px;">
        Initializing video generation
      </div>
    </div>

    <div id="error-container" class="error-message" style="display: none;">
      <strong>Error:</strong> <span id="error-message"></span>
    </div>
  </div>

  <div id="generation-complete" class="generation-complete" style="display: none;">
    <div class="success-message">
      <h3 style="margin-bottom: 15px;">🎉 Videos Generated Successfully!</h3>
      <p>All videos have been generated and are ready for download.</p>
    </div>

    <div id="video-downloads" class="video-downloads">
      <h4 style="margin: 20px 0 15px 0; color: #333;">Download Videos</h4>
      <div id="download-list"></div>
    </div>

    <div style="margin-top: 30px; text-align: center;">
      <button id="download-all" class="btn btn-primary" style="margin-right: 15px;">
        📦 Download All Videos
      </button>
      <button id="start-over" class="btn btn-secondary">
        🔄 Create New Videos
      </button>
    </div>
  </div>

  <div class="navigation" style="margin-top: 30px;">
    <button id="back-to-config" class="btn btn-secondary">
      ← Back to Configuration
    </button>
    <div></div>
  </div>
</div>

<script>
  import type { 
    VideoConfig, 
    GeneratedVideo, 
    VideoGenerationProgress,
    Locale,
    UploadedImage 
  } from '../types';
  import { SUPPORTED_LOCALES } from '../types';
  import { VideoGenerator } from '../utils/videoGenerator';
  import JSZip from 'jszip';
  import { saveAs } from 'file-saver';

  class VideoGenerationManager {
    private videoGenerator: VideoGenerator;
    private generatedVideos: GeneratedVideo[] = [];
    private isGenerating = false;

    constructor() {
      this.videoGenerator = new VideoGenerator();
      this.initializeEventListeners();
    }

    private initializeEventListeners(): void {
      // Back button
      const backBtn = document.getElementById('back-to-config');
      backBtn?.addEventListener('click', () => this.goBackToConfig());

      // Download all button
      const downloadAllBtn = document.getElementById('download-all');
      downloadAllBtn?.addEventListener('click', () => this.downloadAllVideos());

      // Start over button
      const startOverBtn = document.getElementById('start-over');
      startOverBtn?.addEventListener('click', () => this.startOver());

      // Listen for generation start event
      window.addEventListener('startVideoGeneration', (e: any) => {
        this.startGeneration(e.detail);
      });
    }

    private async startGeneration(configData: any): Promise<void> {
      if (this.isGenerating) return;

      this.isGenerating = true;
      this.generatedVideos = [];
      
      try {
        // Reconstruct the full config with file objects
        const config = await this.reconstructConfig(configData);
        
        // Start video generation
        const videos = await this.videoGenerator.generateVideos(
          config,
          (progress) => this.updateProgress(progress)
        );

        this.generatedVideos = videos;
        this.showCompletionScreen();

      } catch (error) {
        this.showError(error instanceof Error ? error.message : 'Unknown error occurred');
      } finally {
        this.isGenerating = false;
      }
    }

    private async reconstructConfig(configData: any): Promise<VideoConfig> {
      // Get base locale
      const baseLocale = SUPPORTED_LOCALES.find(l => l.code === configData.baseLocale);
      if (!baseLocale) throw new Error('Invalid base locale');

      // Get target locales
      const targetLocales = configData.targetLocales
        .map((code: string) => SUPPORTED_LOCALES.find(l => l.code === code))
        .filter(Boolean);

      // Note: In a real implementation, you would need to reconstruct File objects
      // from the stored data. For this demo, we'll create placeholder files.
      const baseImages: UploadedImage[] = configData.baseImages.map((img: any) => ({
        id: img.id,
        url: img.url,
        file: new File([''], img.name, { type: 'image/jpeg' }) // Placeholder
      }));

      const targetImages: Record<string, UploadedImage[]> = {};
      Object.entries(configData.targetImages).forEach(([locale, images]: [string, any]) => {
        targetImages[locale] = images.map((img: any) => ({
          id: img.id,
          url: img.url,
          file: new File([''], img.name, { type: 'image/jpeg' }) // Placeholder
        }));
      });

      return {
        openaiApiKey: configData.openaiApiKey,
        baseLocale,
        targetLocales,
        baseImages,
        targetImages,
        textSlides: configData.textSlides,
        backgroundAudio: configData.backgroundAudio,
        defaultSlideDuration: 3,
        defaultBackgroundColor: '#667eea'
      };
    }

    private updateProgress(progress: VideoGenerationProgress): void {
      const progressFill = document.getElementById('progress-fill');
      const currentLocale = document.getElementById('current-locale');
      const currentStep = document.getElementById('current-step');

      if (progressFill) {
        progressFill.style.width = `${progress.progress}%`;
      }

      if (currentLocale) {
        currentLocale.textContent = `Processing: ${progress.currentLocale}`;
      }

      if (currentStep) {
        currentStep.textContent = progress.currentStep;
      }

      if (progress.error) {
        this.showError(progress.error);
      }

      if (progress.isComplete) {
        this.showCompletionScreen();
      }
    }

    private showError(message: string): void {
      const errorContainer = document.getElementById('error-container');
      const errorMessage = document.getElementById('error-message');

      if (errorContainer && errorMessage) {
        errorMessage.textContent = message;
        errorContainer.style.display = 'block';
      }
    }

    private showCompletionScreen(): void {
      const generationStatus = document.querySelector('.generation-status') as HTMLElement;
      const completionScreen = document.getElementById('generation-complete');

      if (generationStatus) generationStatus.style.display = 'none';
      if (completionScreen) completionScreen.style.display = 'block';

      this.renderDownloadList();
    }

    private renderDownloadList(): void {
      const downloadList = document.getElementById('download-list');
      if (!downloadList) return;

      downloadList.innerHTML = this.generatedVideos.map(video => {
        const locale = SUPPORTED_LOCALES.find(l => l.code === video.locale);
        const flag = locale?.flag || '🏳️';
        const name = locale?.name || video.locale;

        return `
          <div class="video-download">
            <div class="video-info">
              <span class="video-flag">${flag}</span>
              <span>${name}</span>
              <small style="color: #666; margin-left: 10px;">
                ${(video.blob.size / (1024 * 1024)).toFixed(1)} MB
              </small>
            </div>
            <button 
              class="btn btn-primary" 
              onclick="window.videoGenerationManager.downloadVideo('${video.locale}')"
            >
              Download
            </button>
          </div>
        `;
      }).join('');
    }

    public downloadVideo(localeCode: string): void {
      const video = this.generatedVideos.find(v => v.locale === localeCode);
      if (!video) return;

      const url = URL.createObjectURL(video.blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = video.filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }

    private async downloadAllVideos(): Promise<void> {
      if (this.generatedVideos.length === 0) return;

      const zip = new JSZip();

      // Add all videos to zip
      for (const video of this.generatedVideos) {
        zip.file(video.filename, video.blob);
      }

      // Generate and download zip
      try {
        const zipBlob = await zip.generateAsync({ type: 'blob' });
        saveAs(zipBlob, 'app_store_videos.zip');
      } catch (error) {
        console.error('Error creating zip file:', error);
        alert('Error creating zip file. Please try downloading videos individually.');
      }
    }

    private goBackToConfig(): void {
      window.dispatchEvent(new CustomEvent('goToStep2'));
    }

    private startOver(): void {
      // Clear stored data
      sessionStorage.removeItem('videoGeneratorStep1');
      sessionStorage.removeItem('videoGeneratorStep2');
      
      // Reset state
      this.generatedVideos = [];
      this.isGenerating = false;
      
      // Go back to step 1
      window.dispatchEvent(new CustomEvent('goToStep1'));
    }
  }

  // Initialize the manager and make it globally accessible
  document.addEventListener('DOMContentLoaded', () => {
    (window as any).videoGenerationManager = new VideoGenerationManager();
  });
</script>

<style>
  .generation-status {
    text-align: center;
    padding: 40px 20px;
  }

  .generation-complete {
    text-align: center;
  }

  .video-downloads {
    max-width: 600px;
    margin: 0 auto;
  }

  .video-download {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 10px;
    background: #fafafa;
  }

  .video-info {
    display: flex;
    align-items: center;
  }

  .video-flag {
    font-size: 20px;
    margin-right: 10px;
  }

  @media (max-width: 768px) {
    .video-download {
      flex-direction: column;
      gap: 10px;
      text-align: center;
    }
  }
</style>
