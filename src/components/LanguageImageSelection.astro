---
import type { Locale, UploadedImage } from '../types';
import { SUPPORTED_LOCALES } from '../types';
---

<div class="language-image-selection">
  <div class="form-group">
    <label class="form-label" for="openai-key">
      OpenAI API Key *
    </label>
    <input
      type="password"
      id="openai-key"
      class="form-input"
      placeholder="sk-..."
      required
    />
    <small style="color: #666; margin-top: 5px; display: block;">
      Your API key is used only for translation and is not stored.
    </small>
  </div>

  <div class="form-group">
    <label class="form-label" for="base-locale">
      Base Language *
    </label>
    <select id="base-locale" class="form-select" required>
      <option value="">Select base language...</option>
      {SUPPORTED_LOCALES.map(locale => (
        <option value={locale.code}>
          {locale.flag} {locale.name}
        </option>
      ))}
    </select>
  </div>

  <div class="form-group">
    <label class="form-label">
      Base Language Screenshots *
    </label>
    <div class="file-upload" id="base-images-upload">
      <div class="file-upload-icon">📱</div>
      <p>Click to upload or drag and drop screenshots</p>
      <p style="font-size: 14px; color: #666; margin-top: 5px;">
        Supported: JPG, PNG, WebP (Max 50MB each)
      </p>
      <input
        type="file"
        id="base-images-input"
        multiple
        accept="image/jpeg,image/png,image/webp"
      />
    </div>
    <div id="base-images-preview" class="image-preview"></div>
  </div>

  <div class="form-group">
    <label class="form-label">
      Target Languages
    </label>
    <div class="locale-selector" id="target-locales">
      {SUPPORTED_LOCALES.map(locale => (
        <div class="locale-option" data-locale={locale.code}>
          <input
            type="checkbox"
            id={`locale-${locale.code}`}
            value={locale.code}
            style="margin-right: 10px;"
          />
          <span class="locale-flag">{locale.flag}</span>
          <span>{locale.name}</span>
        </div>
      ))}
    </div>
  </div>

  <div id="target-images-section" style="display: none;">
    <h3 style="margin: 30px 0 20px 0; color: #333;">Target Language Screenshots</h3>
    <div id="target-images-uploads"></div>
  </div>

  <div class="navigation">
    <div></div>
    <button id="next-step" class="btn btn-primary" disabled>
      Next: Configure Content →
    </button>
  </div>
</div>

<script>
  import type { Locale, UploadedImage } from '../types';
  import { SUPPORTED_LOCALES, VIDEO_CONSTRAINTS } from '../types';
  import { OpenAITranslator } from '../utils/openai';

  class LanguageImageSelectionManager {
    private baseImages: UploadedImage[] = [];
    private targetImages: Record<string, UploadedImage[]> = {};
    private selectedTargetLocales: string[] = [];
    private baseLocale: string = '';

    constructor() {
      this.initializeEventListeners();
    }

    private initializeEventListeners(): void {
      // OpenAI API Key validation
      const apiKeyInput = document.getElementById('openai-key') as HTMLInputElement;
      apiKeyInput?.addEventListener('input', () => this.validateForm());

      // Base locale selection
      const baseLocaleSelect = document.getElementById('base-locale') as HTMLSelectElement;
      baseLocaleSelect?.addEventListener('change', (e) => {
        this.baseLocale = (e.target as HTMLSelectElement).value;
        this.validateForm();
      });

      // Base images upload
      this.setupImageUpload('base-images-upload', 'base-images-input', 'base-images-preview', 'base');

      // Target locale selection
      const targetLocales = document.getElementById('target-locales');
      targetLocales?.addEventListener('change', (e) => {
        if (e.target instanceof HTMLInputElement && e.target.type === 'checkbox') {
          this.handleTargetLocaleChange(e.target);
        }
      });

      // Next step button
      const nextButton = document.getElementById('next-step');
      nextButton?.addEventListener('click', () => this.proceedToNextStep());
    }

    private setupImageUpload(
      uploadAreaId: string,
      inputId: string,
      previewId: string,
      type: string
    ): void {
      const uploadArea = document.getElementById(uploadAreaId);
      const input = document.getElementById(inputId) as HTMLInputElement;
      const preview = document.getElementById(previewId);

      if (!uploadArea || !input || !preview) return;

      // Click to upload
      uploadArea.addEventListener('click', () => input.click());

      // Drag and drop
      uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
      });

      uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
      });

      uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = Array.from(e.dataTransfer?.files || []);
        this.handleFiles(files, type, preview);
      });

      // File input change
      input.addEventListener('change', (e) => {
        const files = Array.from((e.target as HTMLInputElement).files || []);
        this.handleFiles(files, type, preview);
      });
    }

    private async handleFiles(
      files: File[],
      type: string,
      preview: HTMLElement
    ): Promise<void> {
      const validFiles = files.filter(file => {
        if (!VIDEO_CONSTRAINTS.SUPPORTED_IMAGE_TYPES.includes(file.type)) {
          alert(`${file.name} is not a supported image type.`);
          return false;
        }
        if (file.size > VIDEO_CONSTRAINTS.MAX_FILE_SIZE) {
          alert(`${file.name} is too large. Maximum size is 50MB.`);
          return false;
        }
        return true;
      });

      const uploadedImages: UploadedImage[] = [];

      for (const file of validFiles) {
        const url = URL.createObjectURL(file);
        const id = `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        uploadedImages.push({
          file,
          url,
          id
        });
      }

      if (type === 'base') {
        this.baseImages = uploadedImages;
      } else {
        this.targetImages[type] = uploadedImages;
      }

      this.updateImagePreview(uploadedImages, preview, type);
      this.validateForm();
    }

    private updateImagePreview(
      images: UploadedImage[],
      preview: HTMLElement,
      type: string
    ): void {
      preview.innerHTML = '';

      images.forEach((image, index) => {
        const item = document.createElement('div');
        item.className = 'image-preview-item';
        
        item.innerHTML = `
          <img src="${image.url}" alt="Preview ${index + 1}" />
          <button class="image-preview-remove" data-type="${type}" data-id="${image.id}">×</button>
        `;

        const removeBtn = item.querySelector('.image-preview-remove');
        removeBtn?.addEventListener('click', () => this.removeImage(image.id, type));

        preview.appendChild(item);
      });
    }

    private removeImage(imageId: string, type: string): void {
      if (type === 'base') {
        this.baseImages = this.baseImages.filter(img => img.id !== imageId);
        const preview = document.getElementById('base-images-preview');
        if (preview) this.updateImagePreview(this.baseImages, preview, type);
      } else {
        this.targetImages[type] = this.targetImages[type].filter(img => img.id !== imageId);
        const preview = document.getElementById(`${type}-images-preview`);
        if (preview) this.updateImagePreview(this.targetImages[type], preview, type);
      }
      this.validateForm();
    }

    private handleTargetLocaleChange(checkbox: HTMLInputElement): void {
      const localeCode = checkbox.value;
      const localeOption = checkbox.closest('.locale-option');

      if (checkbox.checked) {
        this.selectedTargetLocales.push(localeCode);
        localeOption?.classList.add('selected');
        this.createTargetImageUpload(localeCode);
      } else {
        this.selectedTargetLocales = this.selectedTargetLocales.filter(code => code !== localeCode);
        localeOption?.classList.remove('selected');
        this.removeTargetImageUpload(localeCode);
        delete this.targetImages[localeCode];
      }

      this.updateTargetImagesSection();
      this.validateForm();
    }

    private createTargetImageUpload(localeCode: string): void {
      const locale = SUPPORTED_LOCALES.find(l => l.code === localeCode);
      if (!locale) return;

      const container = document.getElementById('target-images-uploads');
      if (!container) return;

      const uploadSection = document.createElement('div');
      uploadSection.id = `${localeCode}-upload-section`;
      uploadSection.className = 'form-group';
      
      uploadSection.innerHTML = `
        <label class="form-label">
          ${locale.flag} ${locale.name} Screenshots
          <span style="color: #666; font-weight: normal;">
            (Must match base language count: ${this.baseImages.length})
          </span>
        </label>
        <div class="file-upload" id="${localeCode}-images-upload">
          <div class="file-upload-icon">📱</div>
          <p>Upload ${this.baseImages.length} screenshot(s)</p>
          <input
            type="file"
            id="${localeCode}-images-input"
            multiple
            accept="image/jpeg,image/png,image/webp"
          />
        </div>
        <div id="${localeCode}-images-preview" class="image-preview"></div>
      `;

      container.appendChild(uploadSection);
      this.setupImageUpload(
        `${localeCode}-images-upload`,
        `${localeCode}-images-input`,
        `${localeCode}-images-preview`,
        localeCode
      );
    }

    private removeTargetImageUpload(localeCode: string): void {
      const section = document.getElementById(`${localeCode}-upload-section`);
      section?.remove();
    }

    private updateTargetImagesSection(): void {
      const section = document.getElementById('target-images-section');
      if (!section) return;

      section.style.display = this.selectedTargetLocales.length > 0 ? 'block' : 'none';
    }

    private validateForm(): boolean {
      const apiKey = (document.getElementById('openai-key') as HTMLInputElement)?.value || '';
      const nextButton = document.getElementById('next-step') as HTMLButtonElement;

      const isValid = 
        OpenAITranslator.validateApiKey(apiKey) &&
        this.baseLocale &&
        this.baseImages.length > 0 &&
        this.selectedTargetLocales.every(locale => 
          this.targetImages[locale]?.length === this.baseImages.length
        );

      if (nextButton) {
        nextButton.disabled = !isValid;
      }

      return isValid;
    }

    private proceedToNextStep(): void {
      if (!this.validateForm()) return;

      // Store data in sessionStorage for the next step
      const data = {
        openaiApiKey: (document.getElementById('openai-key') as HTMLInputElement).value,
        baseLocale: this.baseLocale,
        targetLocales: this.selectedTargetLocales,
        baseImages: this.baseImages.map(img => ({
          id: img.id,
          url: img.url,
          name: img.file.name
        })),
        targetImages: Object.fromEntries(
          Object.entries(this.targetImages).map(([locale, images]) => [
            locale,
            images.map(img => ({
              id: img.id,
              url: img.url,
              name: img.file.name
            }))
          ])
        )
      };

      sessionStorage.setItem('videoGeneratorStep1', JSON.stringify(data));
      
      // Trigger custom event to notify parent component
      window.dispatchEvent(new CustomEvent('step1Complete', { detail: data }));
    }

    public getFormData() {
      return {
        openaiApiKey: (document.getElementById('openai-key') as HTMLInputElement)?.value || '',
        baseLocale: this.baseLocale,
        targetLocales: this.selectedTargetLocales,
        baseImages: this.baseImages,
        targetImages: this.targetImages
      };
    }
  }

  // Initialize the manager when the DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new LanguageImageSelectionManager();
  });
</script>
