---
import type { TextSlide } from '../types';
---

<div class="content-configuration">
  <div class="form-group">
    <label class="form-label">
      Number of Text Slides
    </label>
    <div style="display: flex; align-items: center; gap: 15px;">
      <input
        type="number"
        id="slide-count"
        class="form-input"
        min="1"
        max="10"
        value="3"
        style="width: 100px;"
      />
      <span style="color: #666;">slides</span>
    </div>
  </div>

  <div class="form-group">
    <label class="form-label">
      Default Slide Duration
    </label>
    <div style="display: flex; align-items: center; gap: 15px;">
      <input
        type="number"
        id="default-slide-duration"
        class="form-input"
        min="1"
        max="10"
        value="3"
        step="0.5"
        style="width: 100px;"
      />
      <span style="color: #666;">seconds</span>
    </div>
  </div>

  <div class="form-group">
    <label class="form-label">
      Default Background Color
    </label>
    <div style="display: flex; align-items: center; gap: 15px;">
      <input
        type="color"
        id="default-background-color"
        class="color-picker"
        value="#667eea"
      />
      <span id="color-value" style="color: #666;">#667eea</span>
    </div>
  </div>

  <div id="slides-container">
    <h3 style="margin: 30px 0 20px 0; color: #333;">Text Slides Configuration</h3>
    <div id="slides-list"></div>
  </div>

  <div class="form-group">
    <button id="add-slide" class="btn btn-secondary">
      + Add Slide
    </button>
  </div>

  <div class="form-group">
    <label class="form-label">
      Background Audio (Optional)
    </label>
    <div class="file-upload" id="audio-upload">
      <div class="file-upload-icon">🎵</div>
      <p>Click to upload background music</p>
      <p style="font-size: 14px; color: #666; margin-top: 5px;">
        Supported: MP3, WAV, M4A (Max 50MB)
      </p>
      <input
        type="file"
        id="audio-input"
        accept="audio/mp3,audio/wav,audio/m4a"
      />
    </div>
    <div id="audio-preview" style="margin-top: 10px;"></div>
  </div>

  <div class="form-group">
    <div id="duration-validation" class="error-message" style="display: none;">
      Total video duration exceeds 30 seconds. Please adjust slide and image durations.
    </div>
    <div id="duration-display" style="padding: 15px; background: #f8f9fa; border-radius: 8px; margin-top: 10px;">
      <strong>Total Duration: <span id="total-duration">0</span> seconds</strong>
      <div style="font-size: 14px; color: #666; margin-top: 5px;">
        Maximum allowed: 30 seconds
      </div>
    </div>
  </div>

  <div class="navigation">
    <button id="prev-step" class="btn btn-secondary">
      ← Back to Language Selection
    </button>
    <button id="generate-videos" class="btn btn-success" disabled>
      Generate Videos 🎬
    </button>
  </div>
</div>

<script>
  import type { TextSlide, UploadedImage } from '../types';
  import { VIDEO_CONSTRAINTS } from '../types';
  import { VideoGenerator } from '../utils/videoGenerator';

  class ContentConfigurationManager {
    private slides: TextSlide[] = [];
    private backgroundAudio: File | null = null;
    private baseImages: UploadedImage[] = [];
    private slideCounter = 0;

    constructor() {
      this.loadStep1Data();
      this.initializeEventListeners();
      this.generateInitialSlides();
    }

    private loadStep1Data(): void {
      const step1Data = sessionStorage.getItem('videoGeneratorStep1');
      if (step1Data) {
        const data = JSON.parse(step1Data);
        // Note: In a real implementation, you'd need to reconstruct the File objects
        // For now, we'll work with the metadata
        this.baseImages = data.baseImages || [];
      }
    }

    private initializeEventListeners(): void {
      // Slide count change
      const slideCountInput = document.getElementById('slide-count') as HTMLInputElement;
      slideCountInput?.addEventListener('change', () => this.updateSlideCount());

      // Default values change
      const defaultDurationInput = document.getElementById('default-slide-duration') as HTMLInputElement;
      defaultDurationInput?.addEventListener('change', () => this.updateDefaultValues());

      const defaultColorInput = document.getElementById('default-background-color') as HTMLInputElement;
      defaultColorInput?.addEventListener('change', (e) => {
        const colorValue = document.getElementById('color-value');
        if (colorValue) colorValue.textContent = (e.target as HTMLInputElement).value;
        this.updateDefaultValues();
      });

      // Add slide button
      const addSlideBtn = document.getElementById('add-slide');
      addSlideBtn?.addEventListener('click', () => this.addSlide());

      // Audio upload
      this.setupAudioUpload();

      // Navigation buttons
      const prevBtn = document.getElementById('prev-step');
      prevBtn?.addEventListener('click', () => this.goToPreviousStep());

      const generateBtn = document.getElementById('generate-videos');
      generateBtn?.addEventListener('click', () => this.generateVideos());
    }

    private generateInitialSlides(): void {
      const slideCount = parseInt((document.getElementById('slide-count') as HTMLInputElement)?.value || '3');
      for (let i = 0; i < slideCount; i++) {
        this.addSlide();
      }
    }

    private updateSlideCount(): void {
      const slideCount = parseInt((document.getElementById('slide-count') as HTMLInputElement)?.value || '3');
      const currentCount = this.slides.length;

      if (slideCount > currentCount) {
        for (let i = currentCount; i < slideCount; i++) {
          this.addSlide();
        }
      } else if (slideCount < currentCount) {
        for (let i = currentCount - 1; i >= slideCount; i--) {
          this.removeSlide(this.slides[i].id);
        }
      }
    }

    private updateDefaultValues(): void {
      const defaultDuration = parseFloat((document.getElementById('default-slide-duration') as HTMLInputElement)?.value || '3');
      const defaultColor = (document.getElementById('default-background-color') as HTMLInputElement)?.value || '#667eea';

      // Update existing slides with default values if they haven't been customized
      this.slides.forEach(slide => {
        const slideElement = document.getElementById(`slide-${slide.id}`);
        if (slideElement) {
          const durationInput = slideElement.querySelector('.slide-duration') as HTMLInputElement;
          const colorInput = slideElement.querySelector('.slide-color') as HTMLInputElement;
          
          if (durationInput && durationInput.value === slide.duration.toString()) {
            durationInput.value = defaultDuration.toString();
            slide.duration = defaultDuration;
          }
          
          if (colorInput && colorInput.value === slide.backgroundColor) {
            colorInput.value = defaultColor;
            slide.backgroundColor = defaultColor;
          }
        }
      });

      this.validateDuration();
    }

    private addSlide(): void {
      this.slideCounter++;
      const defaultDuration = parseFloat((document.getElementById('default-slide-duration') as HTMLInputElement)?.value || '3');
      const defaultColor = (document.getElementById('default-background-color') as HTMLInputElement)?.value || '#667eea';

      const slide: TextSlide = {
        id: `slide-${this.slideCounter}`,
        text: '',
        duration: defaultDuration,
        backgroundColor: defaultColor,
        associatedImages: [],
        imageDurations: []
      };

      this.slides.push(slide);
      this.renderSlide(slide);
      this.validateDuration();
    }

    private renderSlide(slide: TextSlide): void {
      const slidesList = document.getElementById('slides-list');
      if (!slidesList) return;

      const slideElement = document.createElement('div');
      slideElement.id = `slide-${slide.id}`;
      slideElement.className = 'slide-editor';
      
      slideElement.innerHTML = `
        <div class="slide-header">
          <span class="slide-title">Slide ${this.slides.length}</span>
          <button class="slide-remove" data-slide-id="${slide.id}">Remove</button>
        </div>
        
        <div class="form-group">
          <label class="form-label">Slide Text</label>
          <textarea
            class="form-input slide-text"
            placeholder="Enter your slide text here..."
            rows="3"
            data-slide-id="${slide.id}"
          >${slide.text}</textarea>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
          <div>
            <label class="form-label">Duration (seconds)</label>
            <input
              type="number"
              class="duration-input slide-duration"
              min="0.5"
              max="10"
              step="0.5"
              value="${slide.duration}"
              data-slide-id="${slide.id}"
            />
          </div>
          <div>
            <label class="form-label">Background Color</label>
            <input
              type="color"
              class="color-picker slide-color"
              value="${slide.backgroundColor}"
              data-slide-id="${slide.id}"
            />
          </div>
          <div>
            <label class="form-label">Associated Images</label>
            <select
              class="form-select slide-images"
              multiple
              data-slide-id="${slide.id}"
              style="height: 80px;"
            >
              ${this.baseImages.map((img, index) => `
                <option value="${img.id}">Image ${index + 1}</option>
              `).join('')}
            </select>
          </div>
        </div>

        <div id="image-durations-${slide.id}" class="image-durations" style="display: none;">
          <label class="form-label">Image Display Durations (seconds)</label>
          <div class="image-duration-inputs"></div>
        </div>
      `;

      slidesList.appendChild(slideElement);

      // Add event listeners for this slide
      this.addSlideEventListeners(slide.id);
    }

    private addSlideEventListeners(slideId: string): void {
      const slideElement = document.getElementById(`slide-${slideId}`);
      if (!slideElement) return;

      // Remove button
      const removeBtn = slideElement.querySelector('.slide-remove');
      removeBtn?.addEventListener('click', () => this.removeSlide(slideId));

      // Text change
      const textArea = slideElement.querySelector('.slide-text') as HTMLTextAreaElement;
      textArea?.addEventListener('input', (e) => {
        const slide = this.slides.find(s => s.id === slideId);
        if (slide) slide.text = (e.target as HTMLTextAreaElement).value;
      });

      // Duration change
      const durationInput = slideElement.querySelector('.slide-duration') as HTMLInputElement;
      durationInput?.addEventListener('change', (e) => {
        const slide = this.slides.find(s => s.id === slideId);
        if (slide) {
          slide.duration = parseFloat((e.target as HTMLInputElement).value);
          this.validateDuration();
        }
      });

      // Color change
      const colorInput = slideElement.querySelector('.slide-color') as HTMLInputElement;
      colorInput?.addEventListener('change', (e) => {
        const slide = this.slides.find(s => s.id === slideId);
        if (slide) slide.backgroundColor = (e.target as HTMLInputElement).value;
      });

      // Image selection
      const imageSelect = slideElement.querySelector('.slide-images') as HTMLSelectElement;
      imageSelect?.addEventListener('change', () => {
        this.updateSlideImages(slideId);
      });
    }

    private updateSlideImages(slideId: string): void {
      const slide = this.slides.find(s => s.id === slideId);
      const slideElement = document.getElementById(`slide-${slideId}`);
      if (!slide || !slideElement) return;

      const imageSelect = slideElement.querySelector('.slide-images') as HTMLSelectElement;
      const selectedImages = Array.from(imageSelect.selectedOptions).map(option => option.value);
      
      slide.associatedImages = selectedImages;
      slide.imageDurations = selectedImages.map(() => 2); // Default 2 seconds per image

      // Show/hide image duration inputs
      const imageDurationsContainer = slideElement.querySelector(`#image-durations-${slideId}`) as HTMLElement;
      const inputsContainer = imageDurationsContainer.querySelector('.image-duration-inputs') as HTMLElement;

      if (selectedImages.length > 0) {
        imageDurationsContainer.style.display = 'block';
        inputsContainer.innerHTML = selectedImages.map((imageId, index) => {
          const imageIndex = this.baseImages.findIndex(img => img.id === imageId);
          return `
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 5px;">
              <span>Image ${imageIndex + 1}:</span>
              <input
                type="number"
                class="duration-input image-duration"
                min="0.5"
                max="10"
                step="0.5"
                value="2"
                data-slide-id="${slideId}"
                data-image-index="${index}"
              />
              <span>seconds</span>
            </div>
          `;
        }).join('');

        // Add event listeners for image duration inputs
        inputsContainer.querySelectorAll('.image-duration').forEach(input => {
          input.addEventListener('change', (e) => {
            const target = e.target as HTMLInputElement;
            const imageIndex = parseInt(target.dataset.imageIndex || '0');
            slide.imageDurations[imageIndex] = parseFloat(target.value);
            this.validateDuration();
          });
        });
      } else {
        imageDurationsContainer.style.display = 'none';
      }

      this.validateDuration();
    }

    private removeSlide(slideId: string): void {
      this.slides = this.slides.filter(s => s.id !== slideId);
      const slideElement = document.getElementById(`slide-${slideId}`);
      slideElement?.remove();
      this.validateDuration();
    }

    private setupAudioUpload(): void {
      const uploadArea = document.getElementById('audio-upload');
      const input = document.getElementById('audio-input') as HTMLInputElement;
      const preview = document.getElementById('audio-preview');

      if (!uploadArea || !input || !preview) return;

      uploadArea.addEventListener('click', () => input.click());

      input.addEventListener('change', (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          if (!VIDEO_CONSTRAINTS.SUPPORTED_AUDIO_TYPES.includes(file.type)) {
            alert('Unsupported audio format. Please use MP3, WAV, or M4A.');
            return;
          }
          if (file.size > VIDEO_CONSTRAINTS.MAX_FILE_SIZE) {
            alert('Audio file is too large. Maximum size is 50MB.');
            return;
          }

          this.backgroundAudio = file;
          preview.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
              <span>🎵</span>
              <span>${file.name}</span>
              <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="this.parentElement.parentElement.innerHTML = ''; this.backgroundAudio = null;">Remove</button>
            </div>
          `;
        }
      });
    }

    private validateDuration(): boolean {
      let totalDuration = 0;

      this.slides.forEach(slide => {
        totalDuration += slide.duration;
        totalDuration += slide.imageDurations.reduce((sum, duration) => sum + duration, 0);
      });

      const durationDisplay = document.getElementById('total-duration');
      const validationMessage = document.getElementById('duration-validation');
      const generateButton = document.getElementById('generate-videos') as HTMLButtonElement;

      if (durationDisplay) {
        durationDisplay.textContent = totalDuration.toFixed(1);
      }

      const isValid = totalDuration <= VIDEO_CONSTRAINTS.MAX_DURATION && this.slides.length > 0;

      if (validationMessage) {
        validationMessage.style.display = totalDuration > VIDEO_CONSTRAINTS.MAX_DURATION ? 'block' : 'none';
      }

      if (generateButton) {
        generateButton.disabled = !isValid;
      }

      return isValid;
    }

    private goToPreviousStep(): void {
      window.dispatchEvent(new CustomEvent('goToStep1'));
    }

    private generateVideos(): void {
      if (!this.validateDuration()) return;

      const step1Data = JSON.parse(sessionStorage.getItem('videoGeneratorStep1') || '{}');
      
      const configData = {
        ...step1Data,
        textSlides: this.slides,
        backgroundAudio: this.backgroundAudio
      };

      sessionStorage.setItem('videoGeneratorStep2', JSON.stringify({
        textSlides: this.slides.map(slide => ({
          ...slide,
          // Remove file references for serialization
        })),
        backgroundAudio: this.backgroundAudio ? {
          name: this.backgroundAudio.name,
          size: this.backgroundAudio.size,
          type: this.backgroundAudio.type
        } : null
      }));

      window.dispatchEvent(new CustomEvent('startVideoGeneration', { detail: configData }));
    }

    public getFormData() {
      return {
        textSlides: this.slides,
        backgroundAudio: this.backgroundAudio
      };
    }
  }

  // Initialize the manager when the DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new ContentConfigurationManager();
  });
</script>
