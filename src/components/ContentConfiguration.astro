---
import type { TextSlide } from '../types';
---

<div class="content-configuration">
  <div class="form-group">
    <label class="form-label">
      Number of Text Slides
    </label>
    <div style="display: flex; align-items: center; gap: 15px;">
      <input
        type="number"
        id="slide-count"
        class="form-input"
        min="1"
        max="10"
        value="3"
        style="width: 100px;"
      />
      <span style="color: #666;">slides</span>
    </div>
  </div>

  <div class="form-group">
    <label class="form-label">
      Default Slide Duration
    </label>
    <div style="display: flex; align-items: center; gap: 15px;">
      <input
        type="number"
        id="default-slide-duration"
        class="form-input"
        min="1"
        max="10"
        value="3"
        step="0.5"
        style="width: 100px;"
      />
      <span style="color: #666;">seconds</span>
    </div>
  </div>

  <div class="form-group">
    <label class="form-label">
      Default Background Color
    </label>
    <div style="display: flex; align-items: center; gap: 15px;">
      <input
        type="color"
        id="default-background-color"
        class="color-picker"
        value="#667eea"
      />
      <span id="color-value" style="color: #666;">#667eea</span>
    </div>
  </div>

  <div id="slides-container">
    <h3 style="margin: 30px 0 20px 0; color: #333;">Text Slides Configuration</h3>
    <div id="slides-list"></div>
  </div>

  <div class="form-group">
    <button id="add-slide" class="btn btn-secondary">
      + Add Slide
    </button>
    <button id="debug-images" class="btn btn-secondary" style="margin-left: 10px;">
      🐛 Debug Images
    </button>
  </div>

  <div class="form-group">
    <label class="form-label">
      Background Audio (Optional)
    </label>
    <div class="file-upload" id="audio-upload">
      <div class="file-upload-icon">🎵</div>
      <p>Click to upload background music</p>
      <p style="font-size: 14px; color: #666; margin-top: 5px;">
        Supported: MP3, WAV, M4A (Max 50MB)
      </p>
      <input
        type="file"
        id="audio-input"
        accept="audio/mp3,audio/wav,audio/m4a"
      />
    </div>
    <div id="audio-preview" style="margin-top: 10px;"></div>
  </div>

  <div class="form-group">
    <div id="duration-validation" class="error-message" style="display: none;">
      Total video duration exceeds 30 seconds. Please adjust slide and image durations.
    </div>
    <div id="duration-display" style="padding: 15px; background: #f8f9fa; border-radius: 8px; margin-top: 10px;">
      <strong>Total Duration: <span id="total-duration">0</span> seconds</strong>
      <div style="font-size: 14px; color: #666; margin-top: 5px;">
        Maximum allowed: 30 seconds
      </div>
    </div>
  </div>

  <div class="navigation">
    <button id="prev-step" class="btn btn-secondary">
      ← Back to Language Selection
    </button>
    <button id="generate-videos" class="btn btn-success" disabled>
      Generate Videos 🎬
    </button>
  </div>
</div>

<script>
  import type { TextSlide, UploadedImage } from '../types';
  import { VIDEO_CONSTRAINTS } from '../types';
  import { VideoGenerator } from '../utils/videoGenerator';
  import { appState } from '../utils/appState';

  class ContentConfigurationManager {
    private slides: TextSlide[] = [];
    private backgroundAudio: File | null = null;
    private baseImages: UploadedImage[] = [];
    private slideCounter = 0;

    constructor() {
      this.loadStep1Data();
      this.initializeEventListeners();
      this.generateInitialSlides();

      // Debug logging
      console.log('ContentConfiguration initialized');
      console.log('Base images loaded:', this.baseImages.length);
    }

    private loadStep1Data(): void {
      // Load data from app state instead of sessionStorage
      this.baseImages = appState.getBaseImages();

      // If no images in app state, try to wait for them
      if (this.baseImages.length === 0) {
        console.warn('No base images found in app state. Subscribing to state changes...');
        appState.subscribe((state) => {
          if (state.baseImages && state.baseImages.length > 0) {
            this.baseImages = state.baseImages;
            console.log('Base images updated from state:', this.baseImages.length);
            this.refreshAllSlides();
          }
        });
      }
    }

    private initializeEventListeners(): void {
      // Slide count change
      const slideCountInput = document.getElementById('slide-count') as HTMLInputElement;
      slideCountInput?.addEventListener('change', () => this.updateSlideCount());

      // Default values change
      const defaultDurationInput = document.getElementById('default-slide-duration') as HTMLInputElement;
      defaultDurationInput?.addEventListener('change', () => this.updateDefaultValues());

      const defaultColorInput = document.getElementById('default-background-color') as HTMLInputElement;
      defaultColorInput?.addEventListener('change', (e) => {
        const colorValue = document.getElementById('color-value');
        if (colorValue) colorValue.textContent = (e.target as HTMLInputElement).value;
        this.updateDefaultValues();
      });

      // Add slide button
      const addSlideBtn = document.getElementById('add-slide');
      addSlideBtn?.addEventListener('click', () => this.addSlide());

      // Debug button
      const debugBtn = document.getElementById('debug-images');
      debugBtn?.addEventListener('click', () => this.debugImageAssociation());

      // Audio upload
      this.setupAudioUpload();

      // Navigation buttons
      const prevBtn = document.getElementById('prev-step');
      prevBtn?.addEventListener('click', () => this.goToPreviousStep());

      const generateBtn = document.getElementById('generate-videos');
      generateBtn?.addEventListener('click', () => this.generateVideos());
    }

    private generateInitialSlides(): void {
      const slideCount = parseInt((document.getElementById('slide-count') as HTMLInputElement)?.value || '3');
      for (let i = 0; i < slideCount; i++) {
        this.addSlide();
      }
    }

    private updateSlideCount(): void {
      const slideCount = parseInt((document.getElementById('slide-count') as HTMLInputElement)?.value || '3');
      const currentCount = this.slides.length;

      if (slideCount > currentCount) {
        for (let i = currentCount; i < slideCount; i++) {
          this.addSlide();
        }
      } else if (slideCount < currentCount) {
        for (let i = currentCount - 1; i >= slideCount; i--) {
          this.removeSlide(this.slides[i].id);
        }
      }
    }

    private updateDefaultValues(): void {
      const defaultDuration = parseFloat((document.getElementById('default-slide-duration') as HTMLInputElement)?.value || '3');
      const defaultColor = (document.getElementById('default-background-color') as HTMLInputElement)?.value || '#667eea';

      // Update existing slides with default values if they haven't been customized
      this.slides.forEach(slide => {
        const slideElement = document.getElementById(`slide-${slide.id}`);
        if (slideElement) {
          const durationInput = slideElement.querySelector('.slide-duration') as HTMLInputElement;
          const colorInput = slideElement.querySelector('.slide-color') as HTMLInputElement;
          
          if (durationInput && durationInput.value === slide.duration.toString()) {
            durationInput.value = defaultDuration.toString();
            slide.duration = defaultDuration;
          }
          
          if (colorInput && colorInput.value === slide.backgroundColor) {
            colorInput.value = defaultColor;
            slide.backgroundColor = defaultColor;
          }
        }
      });

      this.validateDuration();
    }

    private addSlide(): void {
      this.slideCounter++;
      const defaultDuration = parseFloat((document.getElementById('default-slide-duration') as HTMLInputElement)?.value || '3');
      const defaultColor = (document.getElementById('default-background-color') as HTMLInputElement)?.value || '#667eea';

      const slide: TextSlide = {
        id: `slide-${this.slideCounter}`,
        text: '',
        duration: defaultDuration,
        backgroundColor: defaultColor,
        associatedImages: [],
        imageDurations: []
      };

      this.slides.push(slide);
      this.renderSlide(slide);
      this.validateDuration();
    }

    private renderSlide(slide: TextSlide): void {
      const slidesList = document.getElementById('slides-list');
      if (!slidesList) return;

      const slideElement = document.createElement('div');
      slideElement.id = `slide-${slide.id}`;
      slideElement.className = 'slide-editor';

      // Create image options HTML
      console.log('Rendering slide with base images:', this.baseImages.length);
      const imageOptionsHtml = this.baseImages.map((img, index) => {
        const isSelected = slide.associatedImages.includes(img.id);
        const fileName = img.file?.name || `Image ${index + 1}`;
        console.log(`Image ${index + 1}: ${fileName}, selected: ${isSelected}`);
        return `<option value="${img.id}" ${isSelected ? 'selected' : ''}>Image ${index + 1} (${fileName})</option>`;
      }).join('');

      let finalImageOptionsHtml = imageOptionsHtml;
      if (this.baseImages.length === 0) {
        console.warn('No base images available for slide rendering');
        // Show a message in the dropdown
        finalImageOptionsHtml = '<option disabled>No images uploaded yet</option>';
      }

      slideElement.innerHTML = `
        <div class="slide-header">
          <span class="slide-title">Slide ${this.slides.indexOf(slide) + 1}</span>
          <button class="slide-remove" data-slide-id="${slide.id}">Remove</button>
        </div>

        <div class="form-group">
          <label class="form-label">Slide Text</label>
          <textarea
            class="form-input slide-text"
            placeholder="Enter your slide text here..."
            rows="3"
            data-slide-id="${slide.id}"
          >${slide.text}</textarea>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
          <div>
            <label class="form-label">Duration (seconds)</label>
            <input
              type="number"
              class="duration-input slide-duration"
              min="0.5"
              max="10"
              step="0.5"
              value="${slide.duration}"
              data-slide-id="${slide.id}"
            />
          </div>
          <div>
            <label class="form-label">Background Color</label>
            <input
              type="color"
              class="color-picker slide-color"
              value="${slide.backgroundColor}"
              data-slide-id="${slide.id}"
            />
          </div>
          <div>
            <label class="form-label">Associated Images</label>
            <select
              class="form-select slide-images"
              multiple
              data-slide-id="${slide.id}"
              style="height: 100px;"
            >
              ${finalImageOptionsHtml}
            </select>
            <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">
              Hold Ctrl/Cmd to select multiple images
            </small>
          </div>
        </div>

        <div id="image-durations-${slide.id}" class="image-durations" style="display: ${slide.associatedImages.length > 0 ? 'block' : 'none'};">
          <label class="form-label">Image Display Durations (seconds)</label>
          <div class="image-duration-inputs"></div>
        </div>

        <div id="image-association-status-${slide.id}" class="image-association-status" style="margin-top: 10px;">
          ${slide.associatedImages.length > 0 ?
            `<small style="color: #28a745; font-weight: 500;">✓ ${slide.associatedImages.length} image(s) associated</small>` :
            `<small style="color: #666;">No images associated with this slide</small>`
          }
        </div>
      `;

      slidesList.appendChild(slideElement);

      // Add event listeners for this slide
      this.addSlideEventListeners(slide.id);

      // Initialize image durations if there are associated images
      if (slide.associatedImages.length > 0) {
        this.updateSlideImages(slide.id);
      }
    }

    private addSlideEventListeners(slideId: string): void {
      const slideElement = document.getElementById(`slide-${slideId}`);
      if (!slideElement) return;

      // Remove button
      const removeBtn = slideElement.querySelector('.slide-remove');
      removeBtn?.addEventListener('click', () => this.removeSlide(slideId));

      // Text change
      const textArea = slideElement.querySelector('.slide-text') as HTMLTextAreaElement;
      textArea?.addEventListener('input', (e) => {
        const slide = this.slides.find(s => s.id === slideId);
        if (slide) slide.text = (e.target as HTMLTextAreaElement).value;
      });

      // Duration change
      const durationInput = slideElement.querySelector('.slide-duration') as HTMLInputElement;
      durationInput?.addEventListener('change', (e) => {
        const slide = this.slides.find(s => s.id === slideId);
        if (slide) {
          slide.duration = parseFloat((e.target as HTMLInputElement).value);
          this.validateDuration();
        }
      });

      // Color change
      const colorInput = slideElement.querySelector('.slide-color') as HTMLInputElement;
      colorInput?.addEventListener('change', (e) => {
        const slide = this.slides.find(s => s.id === slideId);
        if (slide) slide.backgroundColor = (e.target as HTMLInputElement).value;
      });

      // Image selection
      const imageSelect = slideElement.querySelector('.slide-images') as HTMLSelectElement;
      if (imageSelect) {
        console.log('Attaching image selection listener for slide:', slideId);
        console.log('Image select element found:', !!imageSelect);
        console.log('Number of options:', imageSelect.options.length);

        // Add multiple event listeners to ensure selection works
        imageSelect.addEventListener('change', (e) => {
          console.log('Image selection changed for slide:', slideId);
          const selectedValues = Array.from((e.target as HTMLSelectElement).selectedOptions).map(o => o.value);
          console.log('Selected images:', selectedValues);
          this.updateSlideImages(slideId);
        });

        imageSelect.addEventListener('click', () => {
          console.log('Image select clicked for slide:', slideId);
          // Small delay to allow selection to register
          setTimeout(() => {
            const selectedValues = Array.from(imageSelect.selectedOptions).map(o => o.value);
            console.log('Selected images after click:', selectedValues);
            this.updateSlideImages(slideId);
          }, 10);
        });
      } else {
        console.error('Image select element not found for slide:', slideId);
      }
    }

    private updateSlideImages(slideId: string): void {
      const slide = this.slides.find(s => s.id === slideId);
      const slideElement = document.getElementById(`slide-${slideId}`);
      if (!slide || !slideElement) return;

      const imageSelect = slideElement.querySelector('.slide-images') as HTMLSelectElement;
      const selectedImages = Array.from(imageSelect.selectedOptions).map(option => option.value);

      slide.associatedImages = selectedImages;

      // Preserve existing durations or set defaults
      const newDurations: number[] = [];
      selectedImages.forEach((_, index) => {
        if (slide.imageDurations[index] !== undefined) {
          newDurations.push(slide.imageDurations[index]);
        } else {
          newDurations.push(2); // Default 2 seconds per image
        }
      });
      slide.imageDurations = newDurations;

      // Show/hide image duration inputs
      const imageDurationsContainer = slideElement.querySelector(`#image-durations-${slideId}`) as HTMLElement;
      const inputsContainer = imageDurationsContainer.querySelector('.image-duration-inputs') as HTMLElement;

      if (selectedImages.length > 0) {
        imageDurationsContainer.style.display = 'block';
        inputsContainer.innerHTML = selectedImages.map((imageId, index) => {
          const imageIndex = this.baseImages.findIndex(img => img.id === imageId);
          const imageName = this.baseImages[imageIndex]?.file.name || `Image ${imageIndex + 1}`;
          const currentDuration = slide.imageDurations[index] || 2;

          return `
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
              <span style="min-width: 120px; font-weight: 500;">${imageName}:</span>
              <input
                type="number"
                class="duration-input image-duration"
                min="0.5"
                max="10"
                step="0.5"
                value="${currentDuration}"
                data-slide-id="${slideId}"
                data-image-index="${index}"
                style="width: 80px;"
              />
              <span style="color: #666;">seconds</span>
            </div>
          `;
        }).join('');

        // Add event listeners for image duration inputs
        inputsContainer.querySelectorAll('.image-duration').forEach(input => {
          input.addEventListener('change', (e) => {
            const target = e.target as HTMLInputElement;
            const imageIndex = parseInt(target.dataset.imageIndex || '0');
            slide.imageDurations[imageIndex] = parseFloat(target.value);
            this.validateDuration();
          });
        });
      } else {
        imageDurationsContainer.style.display = 'none';
      }

      // Update status indicator
      const statusElement = slideElement.querySelector(`#image-association-status-${slideId}`);
      if (statusElement) {
        statusElement.innerHTML = selectedImages.length > 0 ?
          `<small style="color: #28a745; font-weight: 500;">✓ ${selectedImages.length} image(s) associated</small>` :
          `<small style="color: #666;">No images associated with this slide</small>`;
      }

      this.validateDuration();
    }

    private removeSlide(slideId: string): void {
      this.slides = this.slides.filter(s => s.id !== slideId);
      const slideElement = document.getElementById(`slide-${slideId}`);
      slideElement?.remove();
      this.validateDuration();
    }

    private setupAudioUpload(): void {
      const uploadArea = document.getElementById('audio-upload');
      const input = document.getElementById('audio-input') as HTMLInputElement;
      const preview = document.getElementById('audio-preview');

      if (!uploadArea || !input || !preview) {
        console.error('Audio upload elements not found');
        return;
      }

      console.log('Setting up audio upload functionality');

      uploadArea.addEventListener('click', () => {
        console.log('Audio upload area clicked');
        input.click();
      });

      input.addEventListener('change', (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        console.log('Audio file selected:', file?.name, file?.type, file?.size);

        if (file) {
          // Validate file type
          if (!VIDEO_CONSTRAINTS.SUPPORTED_AUDIO_TYPES.includes(file.type)) {
            alert(`Unsupported audio format: ${file.type}. Please use MP3, WAV, or M4A.`);
            console.error('Unsupported audio type:', file.type);
            return;
          }

          // Validate file size
          if (file.size > VIDEO_CONSTRAINTS.MAX_FILE_SIZE) {
            const sizeMB = (file.size / (1024 * 1024)).toFixed(1);
            alert(`Audio file is too large: ${sizeMB}MB. Maximum size is 50MB.`);
            console.error('Audio file too large:', sizeMB, 'MB');
            return;
          }

          this.backgroundAudio = file;
          console.log('Background audio set:', file.name);

          this.renderAudioPreview(file, preview);
        }
      });
    }

    private renderAudioPreview(file: File, preview: HTMLElement): void {
      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);

      preview.innerHTML = `
        <div id="audio-preview-container" style="display: flex; align-items: center; gap: 10px; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e0e0e0;">
          <span style="font-size: 20px;">🎵</span>
          <div style="flex: 1;">
            <div style="font-weight: 500; color: #333;">${file.name}</div>
            <div style="font-size: 12px; color: #666;">${file.type} • ${fileSizeMB} MB</div>
          </div>
          <button id="remove-audio-btn" class="btn btn-danger" style="padding: 8px 12px; font-size: 12px;">Remove</button>
        </div>
      `;

      // Add remove functionality
      const removeBtn = preview.querySelector('#remove-audio-btn');
      removeBtn?.addEventListener('click', () => {
        console.log('Removing background audio');
        this.backgroundAudio = null;
        preview.innerHTML = '';

        // Clear the file input
        const input = document.getElementById('audio-input') as HTMLInputElement;
        if (input) input.value = '';
      });
    }

    private validateDuration(): boolean {
      let totalDuration = 0;

      this.slides.forEach(slide => {
        totalDuration += slide.duration;
        totalDuration += slide.imageDurations.reduce((sum, duration) => sum + duration, 0);
      });

      const durationDisplay = document.getElementById('total-duration');
      const validationMessage = document.getElementById('duration-validation');
      const generateButton = document.getElementById('generate-videos') as HTMLButtonElement;

      if (durationDisplay) {
        durationDisplay.textContent = totalDuration.toFixed(1);
      }

      // Check if all slides have text
      const hasEmptySlides = this.slides.some(slide => !slide.text.trim());
      const isValidDuration = totalDuration <= VIDEO_CONSTRAINTS.MAX_DURATION;
      const hasSlides = this.slides.length > 0;

      const isValid = isValidDuration && hasSlides && !hasEmptySlides;

      if (validationMessage) {
        if (!isValidDuration) {
          validationMessage.textContent = 'Total video duration exceeds 30 seconds. Please adjust slide and image durations.';
          validationMessage.style.display = 'block';
        } else if (hasEmptySlides) {
          validationMessage.textContent = 'Please add text to all slides before generating videos.';
          validationMessage.style.display = 'block';
        } else if (!hasSlides) {
          validationMessage.textContent = 'Please add at least one slide before generating videos.';
          validationMessage.style.display = 'block';
        } else {
          validationMessage.style.display = 'none';
        }
      }

      if (generateButton) {
        generateButton.disabled = !isValid;
      }

      return isValid;
    }

    private goToPreviousStep(): void {
      window.dispatchEvent(new CustomEvent('goToStep1'));
    }

    private generateVideos(): void {
      if (!this.validateDuration()) return;

      // Update app state with slide configuration
      appState.updateState({
        textSlides: this.slides,
        backgroundAudio: this.backgroundAudio || undefined,
        defaultSlideDuration: parseFloat((document.getElementById('default-slide-duration') as HTMLInputElement)?.value || '3'),
        defaultBackgroundColor: (document.getElementById('default-background-color') as HTMLInputElement)?.value || '#667eea'
      });

      window.dispatchEvent(new CustomEvent('startVideoGeneration'));
    }

    public getFormData() {
      return {
        textSlides: this.slides,
        backgroundAudio: this.backgroundAudio
      };
    }

    private refreshAllSlides(): void {
      // Re-render all slides with updated base images
      console.log('Refreshing all slides with updated images');
      const slidesList = document.getElementById('slides-list');
      if (slidesList) {
        slidesList.innerHTML = '';
        this.slides.forEach(slide => {
          this.renderSlide(slide);
        });
      }
    }

    // Debug method to test image association
    private debugImageAssociation(): void {
      console.log('=== DEBUG IMAGE ASSOCIATION ===');
      console.log('Base images count:', this.baseImages.length);
      console.log('Base images:', this.baseImages.map(img => ({ id: img.id, name: img.file?.name })));
      console.log('Slides count:', this.slides.length);
      this.slides.forEach((slide, index) => {
        console.log(`Slide ${index + 1}:`, {
          id: slide.id,
          text: slide.text,
          associatedImages: slide.associatedImages,
          imageDurations: slide.imageDurations
        });
      });
      console.log('=== END DEBUG ===');
    }
  }

  // Initialize the manager when the DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new ContentConfigurationManager();
  });
</script>
