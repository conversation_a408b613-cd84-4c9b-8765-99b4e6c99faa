export interface Locale {
  code: string;
  name: string;
  flag: string;
}

export interface UploadedImage {
  file: File;
  url: string;
  id: string;
}

export interface TextSlide {
  id: string;
  text: string;
  duration: number;
  backgroundColor: string;
  associatedImages: string[]; // Image IDs
  imageDurations: number[]; // Duration for each associated image
}

export interface VideoConfig {
  openaiApiKey: string;
  baseLocale: Locale;
  targetLocales: Locale[];
  baseImages: UploadedImage[];
  targetImages: Record<string, UploadedImage[]>; // locale code -> images
  textSlides: TextSlide[];
  backgroundAudio?: File;
  defaultSlideDuration: number;
  defaultBackgroundColor: string;
}

export interface TranslatedSlide {
  originalText: string;
  translatedText: string;
  locale: string;
  duration: number;
  backgroundColor: string;
  associatedImages: string[];
  imageDurations: number[];
}

export interface VideoGenerationProgress {
  currentLocale: string;
  currentStep: string;
  progress: number;
  isComplete: boolean;
  error?: string;
}

export interface GeneratedVideo {
  locale: string;
  blob: Blob;
  filename: string;
}

export const SUPPORTED_LOCALES: Locale[] = [
  { code: 'en_US', name: 'English (US)', flag: '🇺🇸' },
  { code: 'en_GB', name: 'English (UK)', flag: '🇬🇧' },
  { code: 'es_ES', name: 'Spanish (Spain)', flag: '🇪🇸' },
  { code: 'es_MX', name: 'Spanish (Mexico)', flag: '🇲🇽' },
  { code: 'fr_FR', name: 'French (France)', flag: '🇫🇷' },
  { code: 'fr_CA', name: 'French (Canada)', flag: '🇨🇦' },
  { code: 'de_DE', name: 'German', flag: '🇩🇪' },
  { code: 'it_IT', name: 'Italian', flag: '🇮🇹' },
  { code: 'pt_BR', name: 'Portuguese (Brazil)', flag: '🇧🇷' },
  { code: 'pt_PT', name: 'Portuguese (Portugal)', flag: '🇵🇹' },
  { code: 'ru_RU', name: 'Russian', flag: '🇷🇺' },
  { code: 'ja_JP', name: 'Japanese', flag: '🇯🇵' },
  { code: 'ko_KR', name: 'Korean', flag: '🇰🇷' },
  { code: 'zh_CN', name: 'Chinese (Simplified)', flag: '🇨🇳' },
  { code: 'zh_TW', name: 'Chinese (Traditional)', flag: '🇹🇼' },
  { code: 'ar_SA', name: 'Arabic', flag: '🇸🇦' },
  { code: 'hi_IN', name: 'Hindi', flag: '🇮🇳' },
  { code: 'th_TH', name: 'Thai', flag: '🇹🇭' },
  { code: 'vi_VN', name: 'Vietnamese', flag: '🇻🇳' },
  { code: 'tr_TR', name: 'Turkish', flag: '🇹🇷' },
  { code: 'pl_PL', name: 'Polish', flag: '🇵🇱' },
  { code: 'nl_NL', name: 'Dutch', flag: '🇳🇱' },
  { code: 'sv_SE', name: 'Swedish', flag: '🇸🇪' },
  { code: 'da_DK', name: 'Danish', flag: '🇩🇰' },
  { code: 'no_NO', name: 'Norwegian', flag: '🇳🇴' },
  { code: 'fi_FI', name: 'Finnish', flag: '🇫🇮' },
  { code: 'he_IL', name: 'Hebrew', flag: '🇮🇱' },
  { code: 'cs_CZ', name: 'Czech', flag: '🇨🇿' },
  { code: 'sk_SK', name: 'Slovak', flag: '🇸🇰' },
  { code: 'hu_HU', name: 'Hungarian', flag: '🇭🇺' },
  { code: 'ro_RO', name: 'Romanian', flag: '🇷🇴' },
  { code: 'bg_BG', name: 'Bulgarian', flag: '🇧🇬' },
  { code: 'hr_HR', name: 'Croatian', flag: '🇭🇷' },
  { code: 'uk_UA', name: 'Ukrainian', flag: '🇺🇦' },
];

export const VIDEO_CONSTRAINTS = {
  MAX_DURATION: 30, // seconds
  WIDTH: 886,
  HEIGHT: 1920,
  FRAME_RATE: 30,
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  SUPPORTED_AUDIO_TYPES: ['audio/mp3', 'audio/wav', 'audio/m4a'],
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
};
