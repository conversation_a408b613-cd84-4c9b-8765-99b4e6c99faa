import type { VideoConfig, UploadedImage, TextSlide, Locale } from '../types';

class AppStateManager {
  private static instance: AppStateManager;
  private state: Partial<VideoConfig> = {};
  private listeners: Array<(state: Partial<VideoConfig>) => void> = [];

  private constructor() {}

  static getInstance(): AppStateManager {
    if (!AppStateManager.instance) {
      AppStateManager.instance = new AppStateManager();
    }
    return AppStateManager.instance;
  }

  updateState(updates: Partial<VideoConfig>): void {
    this.state = { ...this.state, ...updates };
    this.notifyListeners();
  }

  getState(): Partial<VideoConfig> {
    return { ...this.state };
  }

  subscribe(listener: (state: Partial<VideoConfig>) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state));
  }

  // Specific getters for commonly used data
  getBaseImages(): UploadedImage[] {
    return this.state.baseImages || [];
  }

  getTargetImages(): Record<string, UploadedImage[]> {
    return this.state.targetImages || {};
  }

  getTextSlides(): TextSlide[] {
    return this.state.textSlides || [];
  }

  getBaseLocale(): Locale | undefined {
    return this.state.baseLocale;
  }

  getTargetLocales(): Locale[] {
    return this.state.targetLocales || [];
  }

  getOpenAIApiKey(): string {
    return this.state.openaiApiKey || '';
  }

  // Clear state (for starting over)
  clearState(): void {
    this.state = {};
    this.notifyListeners();
  }

  // Validation helpers
  isStep1Complete(): boolean {
    return !!(
      this.state.openaiApiKey &&
      this.state.baseLocale &&
      this.state.baseImages?.length &&
      this.state.targetLocales?.length &&
      this.state.targetImages &&
      this.state.targetLocales.every(locale => 
        this.state.targetImages![locale.code]?.length === this.state.baseImages!.length
      )
    );
  }

  isStep2Complete(): boolean {
    return !!(
      this.isStep1Complete() &&
      this.state.textSlides?.length &&
      this.state.textSlides.every(slide => slide.text.trim().length > 0)
    );
  }
}

// Export singleton instance
export const appState = AppStateManager.getInstance();
