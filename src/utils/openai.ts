import OpenAI from 'openai';
import type { Locale } from '../types';

export class OpenAITranslator {
  private client: OpenAI;

  constructor(apiKey: string) {
    this.client = new OpenAI({
      apiKey,
      dangerouslyAllowBrowser: true
    });
  }

  async translateText(
    text: string,
    fromLocale: Locale,
    toLocale: Locale
  ): Promise<string> {
    // Demo mode for testing
    if (this.client.apiKey === 'demo') {
      return this.generateDemoTranslation(text, fromLocale, toLocale);
    }

    try {
      const prompt = `Translate the following text from ${fromLocale.name} to ${toLocale.name}.
      Keep the same tone and style. If it's marketing copy, maintain the persuasive language.
      Only return the translated text, nothing else.

      Text to translate: "${text}"`;

      const response = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a professional translator specializing in app store marketing copy. Provide accurate, natural translations that maintain the original meaning and marketing appeal.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      });

      const translatedText = response.choices[0]?.message?.content?.trim();

      if (!translatedText) {
        throw new Error('No translation received from OpenAI');
      }

      return translatedText;
    } catch (error) {
      console.error('Translation error:', error);
      throw new Error(`Failed to translate text: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private generateDemoTranslation(text: string, fromLocale: Locale, toLocale: Locale): string {
    // Simple demo translations for testing
    const demoTranslations: Record<string, Record<string, string>> = {
      'es_ES': {
        'Welcome to our amazing app!': '¡Bienvenido a nuestra increíble aplicación!',
        'Discover new features': 'Descubre nuevas características',
        'Get started today': 'Comienza hoy mismo'
      },
      'fr_FR': {
        'Welcome to our amazing app!': 'Bienvenue dans notre application incroyable!',
        'Discover new features': 'Découvrez de nouvelles fonctionnalités',
        'Get started today': 'Commencez dès aujourd\'hui'
      },
      'de_DE': {
        'Welcome to our amazing app!': 'Willkommen in unserer erstaunlichen App!',
        'Discover new features': 'Entdecken Sie neue Funktionen',
        'Get started today': 'Starten Sie noch heute'
      }
    };

    const translations = demoTranslations[toLocale.code];
    if (translations && translations[text]) {
      return translations[text];
    }

    // Fallback: add language prefix to original text
    return `[${toLocale.code}] ${text}`;
  }

  async translateMultipleTexts(
    texts: string[],
    fromLocale: Locale,
    toLocale: Locale
  ): Promise<string[]> {
    const translations: string[] = [];
    
    for (const text of texts) {
      const translation = await this.translateText(text, fromLocale, toLocale);
      translations.push(translation);
      
      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return translations;
  }

  static validateApiKey(apiKey: string): boolean {
    return apiKey === 'demo' || (apiKey.startsWith('sk-') && apiKey.length > 20);
  }
}
