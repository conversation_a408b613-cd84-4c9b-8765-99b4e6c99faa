import OpenAI from 'openai';
import type { Locale } from '../types';

export class OpenAITranslator {
  private client: OpenAI;

  constructor(apiKey: string) {
    this.client = new OpenAI({
      api<PERSON>ey,
      dangerouslyAllowBrowser: true
    });
  }

  async translateText(
    text: string,
    fromLocale: Locale,
    toLocale: Locale
  ): Promise<string> {
    try {
      const prompt = `Translate the following text from ${fromLocale.name} to ${toLocale.name}. 
      Keep the same tone and style. If it's marketing copy, maintain the persuasive language.
      Only return the translated text, nothing else.
      
      Text to translate: "${text}"`;

      const response = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a professional translator specializing in app store marketing copy. Provide accurate, natural translations that maintain the original meaning and marketing appeal.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      });

      const translatedText = response.choices[0]?.message?.content?.trim();
      
      if (!translatedText) {
        throw new Error('No translation received from OpenAI');
      }

      return translatedText;
    } catch (error) {
      console.error('Translation error:', error);
      throw new Error(`Failed to translate text: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async translateMultipleTexts(
    texts: string[],
    fromLocale: Locale,
    toLocale: Locale
  ): Promise<string[]> {
    const translations: string[] = [];
    
    for (const text of texts) {
      const translation = await this.translateText(text, fromLocale, toLocale);
      translations.push(translation);
      
      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return translations;
  }

  static validateApiKey(apiKey: string): boolean {
    return apiKey.startsWith('sk-') && apiKey.length > 20;
  }
}
