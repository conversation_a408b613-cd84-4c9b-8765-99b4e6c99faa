import type { 
  VideoConfig, 
  TranslatedSlide, 
  GeneratedVideo, 
  VideoGenerationProgress,
  Locale,
  UploadedImage 
} from '../types';
import { VIDEO_CONSTRAINTS } from '../types';
import { OpenAITranslator } from './openai';

export class VideoGenerator {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor() {
    this.canvas = document.createElement('canvas');
    this.canvas.width = VIDEO_CONSTRAINTS.WIDTH;
    this.canvas.height = VIDEO_CONSTRAINTS.HEIGHT;
    this.ctx = this.canvas.getContext('2d')!;

    // Set up canvas for high-quality rendering
    this.ctx.imageSmoothingEnabled = true;
    this.ctx.imageSmoothingQuality = 'high';
  }

  async generateVideos(
    config: VideoConfig,
    onProgress: (progress: VideoGenerationProgress) => void
  ): Promise<GeneratedVideo[]> {
    const videos: GeneratedVideo[] = [];
    const translator = new OpenAITranslator(config.openaiApiKey);

    // Generate video for base locale first
    onProgress({
      currentLocale: config.baseLocale.name,
      currentStep: 'Generating base video',
      progress: 0,
      isComplete: false
    });

    const baseVideo = await this.generateSingleVideo(
      config.baseLocale,
      config.textSlides,
      config.baseImages,
      config.backgroundAudio,
      onProgress
    );
    videos.push(baseVideo);

    // Generate videos for target locales
    for (let i = 0; i < config.targetLocales.length; i++) {
      const targetLocale = config.targetLocales[i];
      const targetImages = config.targetImages[targetLocale.code] || [];

      onProgress({
        currentLocale: targetLocale.name,
        currentStep: 'Translating text',
        progress: (i / config.targetLocales.length) * 100,
        isComplete: false
      });

      try {
        // Translate all slide texts
        const translatedSlides: TranslatedSlide[] = [];
        for (const slide of config.textSlides) {
          const translatedText = await translator.translateText(
            slide.text,
            config.baseLocale,
            targetLocale
          );

          translatedSlides.push({
            originalText: slide.text,
            translatedText,
            locale: targetLocale.code,
            duration: slide.duration,
            backgroundColor: slide.backgroundColor,
            associatedImages: slide.associatedImages,
            imageDurations: slide.imageDurations
          });
        }

        onProgress({
          currentLocale: targetLocale.name,
          currentStep: 'Generating video',
          progress: ((i + 0.5) / config.targetLocales.length) * 100,
          isComplete: false
        });

        const video = await this.generateSingleVideo(
          targetLocale,
          translatedSlides,
          targetImages,
          config.backgroundAudio,
          onProgress
        );
        videos.push(video);

      } catch (error) {
        onProgress({
          currentLocale: targetLocale.name,
          currentStep: 'Error',
          progress: (i / config.targetLocales.length) * 100,
          isComplete: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw error;
      }
    }

    onProgress({
      currentLocale: 'All',
      currentStep: 'Complete',
      progress: 100,
      isComplete: true
    });

    return videos;
  }

  private async generateSingleVideo(
    locale: Locale,
    slides: (TranslatedSlide | any)[],
    images: UploadedImage[],
    backgroundAudio?: File,
    onProgress?: (progress: VideoGenerationProgress) => void
  ): Promise<GeneratedVideo> {

    // For now, we'll create a simple video representation
    // In a real implementation, you would use a proper video encoding library
    // like FFmpeg.wasm or a server-side solution

    onProgress?.({
      currentLocale: locale.name,
      currentStep: 'Rendering video frames',
      progress: 50,
      isComplete: false
    });

    // Simulate video generation with a delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Create a video preview (in reality, this would be the actual video)
    const videoPreviewData = await this.createMockVideo(locale, slides, images);
    const blob = new Blob([videoPreviewData], { type: 'text/html' });
    const filename = `${locale.code.toLowerCase()}_video_preview.html`;

    onProgress?.({
      currentLocale: locale.name,
      currentStep: 'Video generation complete',
      progress: 100,
      isComplete: false
    });

    return {
      locale: locale.code,
      blob,
      filename
    };
  }

  private async createMockVideo(locale: Locale, slides: any[], images: UploadedImage[]): Promise<ArrayBuffer> {
    // Create an HTML document that represents the video content
    const htmlContent = this.generateVideoHTML(locale, slides, images);

    // Convert to ArrayBuffer
    const encoder = new TextEncoder();
    return encoder.encode(htmlContent).buffer;
  }

  private generateVideoHTML(locale: Locale, slides: any[], images: UploadedImage[]): string {
    const duration = this.calculateTotalDuration(slides);

    const slideElements = slides.map((slide, index) => {
      const slideImages = slide.associatedImages.map((imageId: string) => {
        const image = images.find(img => img.id === imageId);
        return image ? `<img src="${image.url}" alt="Slide ${index + 1} Image" style="max-width: 100%; height: auto; margin: 10px 0;">` : '';
      }).join('');

      return `
        <div class="slide" style="page-break-after: always; padding: 40px; margin-bottom: 40px; border: 2px solid #ddd; border-radius: 8px;">
          <div class="slide-text" style="background-color: ${slide.backgroundColor}; color: ${this.getContrastColor(slide.backgroundColor)}; padding: 60px; text-align: center; font-size: 48px; font-weight: bold; border-radius: 8px; margin-bottom: 20px;">
            ${slide.translatedText || slide.text}
          </div>
          <div class="slide-duration" style="color: #666; margin-bottom: 10px;">
            Text Duration: ${slide.duration} seconds
          </div>
          ${slideImages ? `
            <div class="slide-images">
              <h4>Associated Images:</h4>
              ${slideImages}
              <div style="color: #666; font-size: 14px;">
                Image Durations: ${slide.imageDurations.join(', ')} seconds
              </div>
            </div>
          ` : ''}
        </div>
      `;
    }).join('');

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>App Store Video Preview - ${locale.name}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
          }
          .header {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .video-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .slide {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          @media print {
            body { background: white; }
            .slide { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🎬 App Store Video Preview</h1>
          <h2>${locale.flag} ${locale.name}</h2>
          <p>This is a preview of your generated video content</p>
        </div>

        <div class="video-info">
          <h3>Video Information</h3>
          <ul>
            <li><strong>Language:</strong> ${locale.name} (${locale.code})</li>
            <li><strong>Total Duration:</strong> ${duration.toFixed(1)} seconds</li>
            <li><strong>Number of Slides:</strong> ${slides.length}</li>
            <li><strong>Resolution:</strong> ${VIDEO_CONSTRAINTS.WIDTH}x${VIDEO_CONSTRAINTS.HEIGHT}</li>
            <li><strong>Generated:</strong> ${new Date().toLocaleString()}</li>
          </ul>
          <p><em>Note: This is a demo preview. In a production environment, this would be an actual MP4 video file.</em></p>
        </div>

        <div class="slides-container">
          <h3>Video Content Preview</h3>
          ${slideElements}
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: white; border-radius: 8px;">
          <p><strong>🎯 Ready for App Store!</strong></p>
          <p>Your video content has been generated and is ready for use in your app store listing.</p>
        </div>
      </body>
      </html>
    `;
  }

  private async generateTextSlideFrame(text: string, backgroundColor: string): Promise<string> {
    // Clear canvas with background color
    this.ctx.fillStyle = backgroundColor;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // Set text properties
    this.ctx.fillStyle = this.getContrastColor(backgroundColor);
    this.ctx.font = 'bold 64px Arial, sans-serif';
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';

    // Draw text with word wrapping
    this.drawWrappedText(
      text,
      this.canvas.width / 2,
      this.canvas.height / 2,
      this.canvas.width - 120,
      80
    );

    // Convert to base64 data URL
    return this.canvas.toDataURL('image/jpeg', 0.8);
  }

  private async generateImageFrame(imageData: UploadedImage): Promise<string> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        // Clear canvas with black background
        this.ctx.fillStyle = '#000000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Calculate scaling to fit image while maintaining aspect ratio
        const scale = Math.min(
          this.canvas.width / img.width,
          this.canvas.height / img.height
        );

        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;
        const x = (this.canvas.width - scaledWidth) / 2;
        const y = (this.canvas.height - scaledHeight) / 2;

        this.ctx.drawImage(img, x, y, scaledWidth, scaledHeight);

        // Convert to base64 data URL
        resolve(this.canvas.toDataURL('image/jpeg', 0.8));
      };

      img.onerror = () => {
        // Fallback: create a placeholder frame
        this.ctx.fillStyle = '#cccccc';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.fillStyle = '#666666';
        this.ctx.font = '32px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Image not available', this.canvas.width / 2, this.canvas.height / 2);
        resolve(this.canvas.toDataURL('image/jpeg', 0.8));
      };

      img.src = imageData.url;
    });
  }

  private calculateTotalDuration(slides: any[]): number {
    let totalDuration = 0;
    slides.forEach(slide => {
      totalDuration += slide.duration || 3;
      if (slide.imageDurations) {
        totalDuration += slide.imageDurations.reduce((sum: number, duration: number) => sum + duration, 0);
      }
    });
    return totalDuration;
  }



  private drawWrappedText(
    text: string,
    x: number,
    y: number,
    maxWidth: number,
    lineHeight: number
  ): void {
    const words = text.split(' ');
    let line = '';
    let currentY = y;

    for (let n = 0; n < words.length; n++) {
      const testLine = line + words[n] + ' ';
      const metrics = this.ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if (testWidth > maxWidth && n > 0) {
        this.ctx.fillText(line, x, currentY);
        line = words[n] + ' ';
        currentY += lineHeight;
      } else {
        line = testLine;
      }
    }
    this.ctx.fillText(line, x, currentY);
  }

  private getContrastColor(backgroundColor: string): string {
    // Simple contrast calculation
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    
    return brightness > 128 ? '#000000' : '#ffffff';
  }



  static validateTotalDuration(slides: any[]): boolean {
    let totalDuration = 0;
    
    for (const slide of slides) {
      totalDuration += slide.duration;
      totalDuration += slide.imageDurations.reduce((sum: number, duration: number) => sum + duration, 0);
    }
    
    return totalDuration <= VIDEO_CONSTRAINTS.MAX_DURATION;
  }
}
