import type { 
  VideoConfig, 
  TranslatedSlide, 
  GeneratedVideo, 
  VideoGenerationProgress,
  Locale,
  UploadedImage 
} from '../types';
import { VIDEO_CONSTRAINTS } from '../types';
import { OpenAITranslator } from './openai';

export class VideoGenerator {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];

  constructor() {
    this.canvas = document.createElement('canvas');
    this.canvas.width = VIDEO_CONSTRAINTS.WIDTH;
    this.canvas.height = VIDEO_CONSTRAINTS.HEIGHT;
    this.ctx = this.canvas.getContext('2d')!;
  }

  async generateVideos(
    config: VideoConfig,
    onProgress: (progress: VideoGenerationProgress) => void
  ): Promise<GeneratedVideo[]> {
    const videos: GeneratedVideo[] = [];
    const translator = new OpenAITranslator(config.openaiApiKey);

    // Generate video for base locale first
    onProgress({
      currentLocale: config.baseLocale.name,
      currentStep: 'Generating base video',
      progress: 0,
      isComplete: false
    });

    const baseVideo = await this.generateSingleVideo(
      config.baseLocale,
      config.textSlides,
      config.baseImages,
      config.backgroundAudio,
      onProgress
    );
    videos.push(baseVideo);

    // Generate videos for target locales
    for (let i = 0; i < config.targetLocales.length; i++) {
      const targetLocale = config.targetLocales[i];
      const targetImages = config.targetImages[targetLocale.code] || [];

      onProgress({
        currentLocale: targetLocale.name,
        currentStep: 'Translating text',
        progress: (i / config.targetLocales.length) * 100,
        isComplete: false
      });

      try {
        // Translate all slide texts
        const translatedSlides: TranslatedSlide[] = [];
        for (const slide of config.textSlides) {
          const translatedText = await translator.translateText(
            slide.text,
            config.baseLocale,
            targetLocale
          );

          translatedSlides.push({
            originalText: slide.text,
            translatedText,
            locale: targetLocale.code,
            duration: slide.duration,
            backgroundColor: slide.backgroundColor,
            associatedImages: slide.associatedImages,
            imageDurations: slide.imageDurations
          });
        }

        onProgress({
          currentLocale: targetLocale.name,
          currentStep: 'Generating video',
          progress: ((i + 0.5) / config.targetLocales.length) * 100,
          isComplete: false
        });

        const video = await this.generateSingleVideo(
          targetLocale,
          translatedSlides,
          targetImages,
          config.backgroundAudio,
          onProgress
        );
        videos.push(video);

      } catch (error) {
        onProgress({
          currentLocale: targetLocale.name,
          currentStep: 'Error',
          progress: (i / config.targetLocales.length) * 100,
          isComplete: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw error;
      }
    }

    onProgress({
      currentLocale: 'All',
      currentStep: 'Complete',
      progress: 100,
      isComplete: true
    });

    return videos;
  }

  private async generateSingleVideo(
    locale: Locale,
    slides: (TranslatedSlide | any)[],
    images: UploadedImage[],
    backgroundAudio?: File,
    onProgress?: (progress: VideoGenerationProgress) => void
  ): Promise<GeneratedVideo> {
    
    // Create a video using canvas and MediaRecorder
    const stream = this.canvas.captureStream(VIDEO_CONSTRAINTS.FRAME_RATE);
    
    // Add audio track if provided
    if (backgroundAudio) {
      const audioContext = new AudioContext();
      const audioBuffer = await this.loadAudioFile(backgroundAudio);
      const audioSource = audioContext.createBufferSource();
      audioSource.buffer = audioBuffer;
      
      const dest = audioContext.createMediaStreamDestination();
      audioSource.connect(dest);
      
      // Add audio track to video stream
      dest.stream.getAudioTracks().forEach(track => {
        stream.addTrack(track);
      });
    }

    this.recordedChunks = [];
    this.mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'video/webm;codecs=vp9'
    });

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.recordedChunks.push(event.data);
      }
    };

    return new Promise((resolve, reject) => {
      this.mediaRecorder!.onstop = () => {
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const filename = `${locale.code.toLowerCase()}_video.webm`;
        
        resolve({
          locale: locale.code,
          blob,
          filename
        });
      };

      this.mediaRecorder!.onerror = (event) => {
        reject(new Error('MediaRecorder error'));
      };

      this.mediaRecorder!.start();
      this.renderVideoFrames(slides, images).then(() => {
        this.mediaRecorder!.stop();
      }).catch(reject);
    });
  }

  private async renderVideoFrames(
    slides: (TranslatedSlide | any)[],
    images: UploadedImage[]
  ): Promise<void> {
    const frameInterval = 1000 / VIDEO_CONSTRAINTS.FRAME_RATE;
    
    for (const slide of slides) {
      // Render text slide
      await this.renderTextSlide(
        slide.translatedText || slide.text,
        slide.backgroundColor,
        slide.duration * 1000,
        frameInterval
      );

      // Render associated images
      for (let i = 0; i < slide.associatedImages.length; i++) {
        const imageId = slide.associatedImages[i];
        const duration = slide.imageDurations[i] * 1000;
        const image = images.find(img => img.id === imageId);
        
        if (image) {
          await this.renderImageSlide(image, duration, frameInterval);
        }
      }
    }
  }

  private async renderTextSlide(
    text: string,
    backgroundColor: string,
    duration: number,
    frameInterval: number
  ): Promise<void> {
    const frames = Math.floor(duration / frameInterval);
    
    for (let frame = 0; frame < frames; frame++) {
      // Clear canvas
      this.ctx.fillStyle = backgroundColor;
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      
      // Draw text
      this.ctx.fillStyle = this.getContrastColor(backgroundColor);
      this.ctx.font = 'bold 48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      
      // Word wrap text
      this.drawWrappedText(
        text,
        this.canvas.width / 2,
        this.canvas.height / 2,
        this.canvas.width - 100,
        60
      );
      
      await this.waitForFrame(frameInterval);
    }
  }

  private async renderImageSlide(
    imageData: UploadedImage,
    duration: number,
    frameInterval: number
  ): Promise<void> {
    const frames = Math.floor(duration / frameInterval);
    const img = new Image();
    
    return new Promise((resolve) => {
      img.onload = async () => {
        for (let frame = 0; frame < frames; frame++) {
          // Clear canvas
          this.ctx.fillStyle = '#000000';
          this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
          
          // Calculate scaling to fit image while maintaining aspect ratio
          const scale = Math.min(
            this.canvas.width / img.width,
            this.canvas.height / img.height
          );
          
          const scaledWidth = img.width * scale;
          const scaledHeight = img.height * scale;
          const x = (this.canvas.width - scaledWidth) / 2;
          const y = (this.canvas.height - scaledHeight) / 2;
          
          this.ctx.drawImage(img, x, y, scaledWidth, scaledHeight);
          
          await this.waitForFrame(frameInterval);
        }
        resolve();
      };
      
      img.src = imageData.url;
    });
  }

  private drawWrappedText(
    text: string,
    x: number,
    y: number,
    maxWidth: number,
    lineHeight: number
  ): void {
    const words = text.split(' ');
    let line = '';
    let currentY = y;

    for (let n = 0; n < words.length; n++) {
      const testLine = line + words[n] + ' ';
      const metrics = this.ctx.measureText(testLine);
      const testWidth = metrics.width;
      
      if (testWidth > maxWidth && n > 0) {
        this.ctx.fillText(line, x, currentY);
        line = words[n] + ' ';
        currentY += lineHeight;
      } else {
        line = testLine;
      }
    }
    this.ctx.fillText(line, x, currentY);
  }

  private getContrastColor(backgroundColor: string): string {
    // Simple contrast calculation
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    
    return brightness > 128 ? '#000000' : '#ffffff';
  }

  private async loadAudioFile(file: File): Promise<AudioBuffer> {
    const audioContext = new AudioContext();
    const arrayBuffer = await file.arrayBuffer();
    return await audioContext.decodeAudioData(arrayBuffer);
  }

  private waitForFrame(interval: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, interval));
  }

  static validateTotalDuration(slides: any[]): boolean {
    let totalDuration = 0;
    
    for (const slide of slides) {
      totalDuration += slide.duration;
      totalDuration += slide.imageDurations.reduce((sum: number, duration: number) => sum + duration, 0);
    }
    
    return totalDuration <= VIDEO_CONSTRAINTS.MAX_DURATION;
  }
}
