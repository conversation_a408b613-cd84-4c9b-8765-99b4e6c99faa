---
import Layout from '../layouts/Layout.astro';
import LanguageImageSelection from '../components/LanguageImageSelection.astro';
import ContentConfiguration from '../components/ContentConfiguration.astro';
import VideoGenerator from '../components/VideoGenerator.astro';
---

<Layout title="App Store Video Generator">
	<div class="container">
		<div class="header">
			<h1>App Store Video Generator</h1>
			<p>Create professional preview videos for your app in multiple languages</p>
		</div>

		<div class="step-indicator">
			<div class="step active" id="step-1-indicator">
				<div class="step-number">1</div>
				<span>Language & Images</span>
			</div>
			<div class="step" id="step-2-indicator">
				<div class="step-number">2</div>
				<span>Content Configuration</span>
			</div>
			<div class="step" id="step-3-indicator">
				<div class="step-number">3</div>
				<span>Generate Videos</span>
			</div>
		</div>

		<div class="card">
			<div id="step-1" class="step-content">
				<LanguageImageSelection />
			</div>

			<div id="step-2" class="step-content" style="display: none;">
				<ContentConfiguration />
			</div>

			<div id="step-3" class="step-content" style="display: none;">
				<VideoGenerator />
			</div>
		</div>
	</div>
</Layout>

<script>
	class AppController {
		private currentStep = 1;

		constructor() {
			this.initializeEventListeners();
			this.updateStepValidation();
		}

		private initializeEventListeners(): void {
			// Listen for step completion events
			window.addEventListener('step1Complete', () => {
				this.goToStep(2);
			});

			window.addEventListener('startVideoGeneration', () => {
				this.goToStep(3);
			});

			// Listen for navigation events
			window.addEventListener('goToStep1', () => {
				this.goToStep(1);
			});

			window.addEventListener('goToStep2', () => {
				this.goToStep(2);
			});
		}

		private goToStep(step: number): void {
			if (step < 1 || step > 3) return;

			// Hide all step contents
			for (let i = 1; i <= 3; i++) {
				const stepContent = document.getElementById(`step-${i}`);
				const stepIndicator = document.getElementById(`step-${i}-indicator`);

				if (stepContent) {
					stepContent.style.display = i === step ? 'block' : 'none';
				}

				if (stepIndicator) {
					stepIndicator.classList.remove('active', 'completed');
					if (i === step) {
						stepIndicator.classList.add('active');
					} else if (i < step) {
						stepIndicator.classList.add('completed');
					}
				}
			}

			this.currentStep = step;

			// Scroll to top
			window.scrollTo({ top: 0, behavior: 'smooth' });
		}

		private updateStepValidation(): void {
			// This method can be used to validate steps and update UI accordingly
			// For now, it's just a placeholder
		}
	}

	// Initialize the app controller when the DOM is loaded
	document.addEventListener('DOMContentLoaded', () => {
		new AppController();
	});
</script>
